<template>
    <view class="container">
        <view class="back">
            <uni-icons @click="back()" type="back" size="36"></uni-icons>
        </view>

        <view style="width: 100%; text-align: center; font-size: 24px;">
            <text>生產排程（待排程生產）</text>
        </view>
		<view class="tot_qty_sum">
			<text>縂雙數：{{tot_qty_sum}}</text>
		</view>

        <view class="search">

			<view class="flex_box">
				<text style="width: 70px;">出货时间</text>
				<picker  mode="date" :value="startTime"   :start="startDate" :end="endDate" @change="bindDateChange">
					<view style="padding: 8px;background-color: white;width: 90px" v-if="startTime === ''" class="uni-input">请选择</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="startTime != ''" class="uni-input">{{startTimeText}}</view>
				</picker>
				<picker  mode="date" :value="endTime" @change="bindEndDateChange" style="margin-left: 10px">
					<view style="padding: 8px;background-color: white; width: 90px" v-if="endTime === ''" class="uni-input">请选择</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="endTime != ''" class="uni-input">{{endTimeText}}</view>
				</picker>
			</view>


			<view class="flex_box" style="width: 150px">
				<text style="margin-left: 10px;">品牌</text>
				<view class="inputDate">
					<uni-data-select
							style="width:100px"
							v-model="brand"
							:localdata="brandList"
							@change="bindBrandChange"

					></uni-data-select>
				</view>
			</view>

			<view class="flex_box" style="width:200px;">
				<text style="width: 50px;">样品<br>类型</text>
				<view>
					<uni-data-select v-model="devType" :localdata="devTypes"
									 :clear="false"
									 emptyTips="請選擇"
									 style="background-color: white;width:150px;margin-left:5px"></uni-data-select>
				</view>
			</view>


			<view class="flex_box" style="width: 180px">
				<text style="width:50px">样品单号</text>
				<view>
					<uni-combox
							:candidates="searchOrdNoList"
							placeholder="请选择单号"
							v-model="searchOrdNo"
							@input="bindOrdNoListChange"
							style="white;margin-left: 5px"
					></uni-combox>
					<!--<view class="result-box">
						<text>所选城市为：{{ city }}</text>
					</view>-->
				</view>

				<!--	<picker mode="selector" :range="searchOrdNoList" @change="bindOrdNoListChange">
						<input type="text" v-model="searchOrdNo" class="ordNoClass" />
					</picker>-->
            </view>


			<view style="display:flex;align-items: center;width:180px;margin-left: 15px">
				<text>生产线</text>
				<view>
					<uni-data-select
							v-model="pdLine"
							:localdata="lineList"
							@change="bindLineListChange"
							style="width: 120px;background-color: white;margin-left: 5px"
					></uni-data-select>
				</view>
			</view>


			<view style="display:flex;align-items: center;width:180px;margin-left: 15px;">
				<text>阶段</text>
				<view>
					<uni-data-select
							v-model="phase"
							:localdata="phaseTypes"
							@change="binPhaseTypesChange"
							style="width: 100px;background-color: white;white;margin-left: 5px"
					></uni-data-select>
				</view>
			</view>





            <uv-button @click="query()" style="margin-left: 25px;" type="primary" text="查询"></uv-button>
        </view>
		
		<view class="table-container">
            <scroll-view
                    scroll-y
                    scroll-x
                    @scrolltolower="loadMoreData"
                    :scroll-top="0"
                    style="height: 99%;width:100%;"
                    class="scorll-box"
            >
			<table >
				<thead>
					<tr style="background-color: #F0F0F0;height: 5vh;">
						<th class="rowClass" align="center">項次</th>
						<th class="rowClass" align="center">上綫日期</th>
						<th class="rowClass" align="center">形體確認</th>
						<th class="rowClass" align="center">模具確認</th>

						<th class="rowClass" align="center">階段</th>
						<th class="rowClass" align="center">綫別</th>

						<th class="rowClass" align="center">面料交期</th>
						<th class="rowClass" align="center">底料交期</th>
						<th class="rowClass" align="center">樣品單號</th>
						<th class="rowClass" align="center">樣品類型</th>
						<th class="rowClass" align="center">鞋圖</th>
						<th class="rowClass" align="center">面師/底師</th>
						<th class="rowClass" align="center">業務</th>
						<th class="rowClass" align="center">型體</th>
						<th class="rowClass" align="center">SKU</th>
						<th class="rowClass" align="center">楦頭編號</th>
						<th class="rowClass" align="center">楦頭數量</th>
						<th class="rowClass" align="center">派工日</th>
						<th class="rowClass" align="center">出貨日</th>
						<th class="rowClass" align="center">樣品雙數</th>

					<!--	<th class="rowClass" align="center">編輯投入</th>-->
					</tr>
				</thead>
				
				<tbody>
					<tr v-for="(item, index) in dataList" :key="item"  class="datalist-tr">
						<td class="rowClass" align="center">{{ item.item_no }}</td>
						<td class="rowClass" align="center">
							<view>{{ item.online_date }}</view>
							<view>
								<uv-button text="生控投入" type="error" @click="edit(item.ord_no,item.item_no)"></uv-button>
							</view>
						</td>
						<td class="rowClass" align="center">{{ item.model_cfm_flag }}</td>
						<td class="rowClass" align="center">{{ item.dev_types }}</td>

						<td class="rowClass" align="center">{{ item.phase}}</td>
						<td class="rowClass" align="center"  style="width: 50px!important;">{{item.pd_line.substr(0,2)}} <br/> {{ item.pd_line.substr(2) }}</td>

						<td class="rowClass" align="center">{{ item.pur_u_flag }}</td>
						<td class="rowClass" align="center">{{ item.pur_s_flag }}</td>
						<td class="rowClass" align="center">{{ item.ord_no }}</td>
						<td class="rowClass" align="center">{{ item.dev_type }}</td>
						<td class="rowClass" align="center"><img style="width: 50px;" :src="'data:image/jpg;base64,' + item.model_pic " alt="鞋图"/></td>
						<td class="rowClass" align="center">{{ item.upper_ser }}/{{ item.sole_ser }}</td>
					<!--	<td class="rowClass" align="center">{{ item.dutyer }}</td>-->
						<td class="rowClass" align="center" >
							{{ formattedBRLetters(item.dutyer) }}<br/>
							{{ formattedBRNumbers(item.dutyer) }}
						</td>
						<td class="rowClass" align="center">{{ item.model_no }}</td>
						<td class="rowClass" align="center">{{ item.sku_no }}</td>
						<td class="rowClass" align="center">{{ item.last_no }}</td>
						<td class="rowClass" align="center">{{ item.app_qty }}</td>
						<td class="rowClass" align="center">{{ item.wo_date }}</td>
						<td class="rowClass" align="center">{{ item.shp_date }}</td>
						<td class="rowClass" align="center">{{ item.tot_qty }}</td>

					<!--	<td class="rowClass" align="center">
							<uv-button text="生控投入" type="error" @click="edit(item.ord_no,item.item_no)"></uv-button>
						</td>-->
					</tr>
				</tbody>
			</table>
            </scroll-view>
		</view>

        <view class="page-bottom">
           <!-- <uni-pagination show-icon="true" :total="pageCount" :current="firstPageNo" :pageSize="firstPageSize"
                            @change="firstChange"></uni-pagination>-->
			共{{pageCount}}条
        </view>
    </view>
	<view class="tip-popup">
	    <uni-popup ref="tipPopup" type="message">
	        <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
	    </uni-popup>
	</view>

</template>

<script setup>
import {
    onMounted,
    ref,
    reactive,
    watch
} from 'vue'
import {
    onPullDownRefresh
} from '@dcloudio/uni-app'
import {
    onShow
} from '@dcloudio/uni-app';
import urlPrefix from '@/pages/common/urlPrefix.js'

//第几页
const firstPageNo = ref(1)
const firstPageSize = ref(500)
const pageCount = ref(0)

//表单数据
const dataList = ref([])

const insUs = uni.getStorageSync("loUserNo")
const insName = uni.getStorageSync("loginUserName")

//消息提示
const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

//日期选择
const endTime = ref(new Date().getTime())
const date = new Date();
date.setHours(date.getHours() - 24);
const startTime = ref(date.getTime())
const datetimePickerSt = ref()
const datetimePickerEnd = ref()
const startTimeText = ref('2024/06/03')
const endTimeText = ref('2024/06/03')
const brandList = ref([])
const brand = ref()
const devType = ref()
const devTypes = ref()
const tot_qty_sum = ref()
const tableData = ref([]);
const searchOrdNo = ref()
const searchOrdNoList = ref([])
const hasMoreData = ref(true)


const phaseTypes = ref([
 /*   {text: 'ALL', value: ""},*/
    {text: '開發樣品', value: "開發樣品"},
    {text: '銷售樣品', value: "銷售樣品"},
    {text: '量產樣品', value: "量產樣品"},
    {text: '量產試做', value: "量產試做"},
    {text: '量產訂單', value: "量產訂單"},
    {text: 'TECH', value: "TECH"},
])
const phase = ref()

const pdLine = ref()

const lineList = ref([
/*    {text: "ALL", value: ''},*/
        {text: "A", value: 'A'},
        {text: "A線", value: 'A線'},
        {text: "A線DRESS", value: 'A線DRESS'},
        {text: "B線DRESS", value: 'B線DRESS'},
])

function bindLineListChange(e) {
    pdLine.value = e
}


// 换行方法
function formattedBRNumbers(text) {
    const numbers = text.match(/\d+/g) || []; // 提取数字
    return  numbers[0];
};

function formattedBRLetters(text){
    const letters = text.match(/[^\d]+/g) || []; // 提取非数字字符
    return letters[0]
};


function bindOrdNoListChange(e) {
    console.log("e",e)
    searchOrdNo.value = searchOrdNoList.value[e.detail.value]

}

function bindBrandChange(e) {
    brand.value = e
    searchOrdNoList.value = []
	if(e === "所有品牌"){
        for (const item of dataList.value) {
                /*let obj = {
                    value: item.ord_no,
                    text: item.ord_no
                }*/
                searchOrdNoList.value.push(item.ord_no)
        }
	}else {
        for (const item of dataList.value) {
            if (item.model_no.indexOf(e) > -1) {
               /* let obj = {
                    value: item.ord_no,
                    text: item.ord_no
                }*/
                searchOrdNoList.value.push(item.ord_no)
            }
        }
	}

    searchOrdNoList.value.sort((a, b) => {
        if (a < b) {
            return -1;
        }
        if (a > b) {
            return 1;
        }
        return 0;
    });
}

// 获取品牌列表
function getBrands() {
    uni.request({
        url: urlPrefix + "/first/getBrandsPlus",
        method: "POST"
    }).then(res => {
        let arr = []
   /*     brandList.value = res.data.data.map(item=>item.data.map(obj=>obj)) ? res.data.data : []*/
        for (const item of  res.data.data) {
            item.data.forEach(i=>{
                arr.push({value:i,text:i})
            })
        }
        brandList.value = arr;

    }).catch(err => {
        console.log(err)
    })
}

function bindDateChange(e){
    startTime.value = new Date(e.detail.value).getTime();
    startTimeText.value = e.detail.value;
}

function bindEndDateChange(e){
    endTime.value = new Date(e.detail.value).getTime();
    endTimeText.value = e.detail.value;
}


function openSt() {
	datetimePickerSt.value.open();
}

function confirmSt(e) {
	console.log('confirm', e);
	// 创建一个新的日期对象e
	var date = new Date(e.value);
	
	// 获取年、月、日
	var year = date.getFullYear();
	var month = date.getMonth() + 1; // 月份从0开始，需要加1
	var day = date.getDate();
	
	// 格式化日期为yyyy/MM/DD的样式
	var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
	
	startTimeText.value = formattedDate;
}


function openEnd() {
	datetimePickerEnd.value.open();
}

function confirmEnd(e) {
	// 创建一个新的日期对象e
	var date = new Date(e.value);
	
	// 获取年、月、日
	var year = date.getFullYear();
	var month = date.getMonth() + 1; // 月份从0开始，需要加1
	var day = date.getDate();
	
	// 格式化日期为yyyy/MM/DD的样式
	var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
	
	endTimeText.value = formattedDate;
}

function edit(ordNo,itemNo){
	uni.showModal({
	    title: '提示',
	    content: '确定要投入吗？',
	    success: function (res) {
	        if (res.confirm) {
	            console.log('用户点击确定');
	            uni.request({
	                url: urlPrefix + "/pending/edit",
	                data: {
	                    "ord_no": ordNo,
	                    "item_no": itemNo
	                },
	                method: "POST"
	            }).then(res => {
					showTip('success', "投入成功！");
	                uni.hideLoading();
	            }).catch(err => {
	                console.log(err)
	                uni.hideLoading();
	                uni.showToast({
	                    title: '操作失败..',
	                    icon: "error"
	                });
	            })
	        } else if (res.cancel) {
	            console.log('用户点击取消');
	        }
	    }
	});
}



// 加载数据方法
function loadData() {
    if (!hasMoreData.value) return; // 没有更多数据时，不再加载
    // 模拟异步请求分页数据
    setTimeout(() => {
        getMockData(firstPageNo.value, firstPageSize.value);
    }, 1000);
}

// 滑动到底部触发加载更多
function loadMoreData(event) {
    console.log(event)
    if(event.detail.direction === "bottom"){
        loadData();
    }
}

// 模拟数据获取
function  getMockData() {
    uni.showLoading({
        title: '加載中',
        mask: true // 设置遮罩层
    });
    uni.request({
        url: urlPrefix + "/pending/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
            "startTime": startTime.value,
            "endTime": endTime.value,
            "brand": brand.value,
            "ordNo": searchOrdNo.value,
            "devType": devType.value,
            "phase":phase.value,
            "pdLine":pdLine.value
        },
        method: "GET"
    }).then(res => {
        if(res.data.data.list.length > 0){
            tot_qty_sum.value = res.data.data.list[0].tot_qty_sum;
        }
        dataList.value =[...dataList.value, ...res.data.data.list] ;
        pageCount.value = res.data.data.total;

        // 如果返回数据小于 pageSize，说明没有更多数据了
        if (res.data.data.list.length < firstPageSize.value) {
            hasMoreData.value = false;
            uni.showToast({
                title: '到底了..',
            });
        }
        // 增加页码
        firstPageNo.value += 1;
        uni.hideLoading();
    }).catch(err => {
        console.log(err)
        uni.hideLoading();
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}


function query(){
	firstPageNo.value = 1;
	getData();
}

function getData() {
    uni.showLoading({
        title: '加載中',
        mask: true // 设置遮罩层
    });
    tableData.value = [];
    uni.request({
        url: urlPrefix + "/pending/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
            "startTime": startTime.value,
            "endTime": endTime.value,
            "brand": brand.value,
			"ordNo": searchOrdNo.value,
            "devType": devType.value,
            "phase":phase.value,
            "pdLine":pdLine.value
        },
        method: "GET"
    }).then(res => {
		if(res.data.data.list.length > 0){
			tot_qty_sum.value = res.data.data.list[0].tot_qty_sum;
		}else{
			tot_qty_sum.value = 0;
		}
        dataList.value = res.data.data.list;
        pageCount.value = res.data.data.total;
        searchOrdNoList.value = dataList.value.map(item=>{
          /*  return {
                value:item.ord_no
				,text:item.ord_no
			}*/
          	return item.ord_no
		});
        searchOrdNoList.value.sort((a, b) => {
            if (a < b) {
                return -1;
            }
            if (a > b) {
                return 1;
            }
            return 0;
        });
        // 增加页码
        firstPageNo.value += 1;
        uni.hideLoading();
    }).catch(err => {
        console.log(err)
        uni.hideLoading();
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}


function firstChange(e) {
    uni.showLoading({
        title: '加載中',
        mask: true // 设置遮罩层
    });
    firstPageNo.value = e.current;
    uni.request({
        url: urlPrefix + "/pending/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
            "startTime": startTime.value,
            "endTime": endTime.value,
            "brand": brand.value,
            "devType": devType.value,
            "phase":phase.value,
            "pdLine":pdLine.value
        },
        method: "GET"
    }).then(res => {
		if(res.data.data.list.length > 0){
			tot_qty_sum.value = res.data.data.list[0].tot_qty_sum;
		}else{
			tot_qty_sum.value = 0;
		}
        pageCount.value = res.data.data.total;
		dataList.value = res.data.data.list;
        uni.hideLoading();
    }).catch(err => {
        uni.hideLoading();
        console.log(err)
    })
}


//获取数据
function queryAllDevType() {
    uni.request({
        url: urlPrefix + "/pending/queryAllDevType",
        method: "GET"
    }).then(res => {
        console.log(res.data.data);
        devTypes.value = res.data.data;
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

// 提示信息
function showTip(type, message) {
    tipType.value = type
    tipMessage.value = message
    tipPopup.value.open()
}


// 返回上一页
function back() {
    let back = getCurrentPages();
    if(back && back.length > 1) {
    	uni.navigateBack({  
    	  delta: 1  
    	});  
    } else {  
    	history.back();  
    }
}


//预加载
onMounted(async () => {
    if(1 == 1){
        // 创建一个新的日期对象e
        var date = new Date();

        // 获取年、月、日
        var year = date.getFullYear();
        var month = date.getMonth() + 1; // 月份从0开始，需要加1
        var day = date.getDate();

        // 格式化日期为yyyy/MM/DD的样式
        var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);

        endTimeText.value = formattedDate;
    }
    if(1 == 1){
        // 创建一个新的日期对象e
        var date = new Date();

        // 获取当前日期的天数
        var temp = date.getDate();

        // 将日期减去一天
        date.setDate(temp - 1);
        // 获取年、月、日
        var year = date.getFullYear();
        var month = date.getMonth() + 1; // 月份从0开始，需要加1
        var day = date.getDate();

        var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
        startTimeText.value = formattedDate;
    }
    getData();
	queryAllDevType();
    getBrands();
})

//登录校验
function loginCheck(){
    if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
        uni.navigateTo({
            url: `/pages/login/login`,
            animationType: 'pop-in',
            animationDuration: 200
        })
    }
}

onShow(async (props) => {
    loginCheck();
})
</script>

<style lang="scss">
page {
    width: 100%;
    height: 100%;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;
}

.back {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 2.5%;
        top: 2%;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        z-index: 1;

        &:active {
            transform: scale(0.97);
            box-shadow: 0 0 1px gray;
        }
}

.tot_qty_sum{
	display: flex;
	justify-content: space-evenly;
	align-items: center;
	position: absolute;
	right: 1.5%;
	top: 3%;
	cursor: pointer;
	z-index: 1;
}

.search {

    display: flex;
    align-items: center;
	flex-wrap: wrap;
	justify-content: start;
	margin-top: 2%;
  /*  margin-left: 1%;
    margin-top: 2%;*/

    .inputDate{
    /*    width: 12%;*/
        margin-left: 5px;
    }

    .search button{
        width: 20%;
    }
}

.container {
    width: 100%;
    height: 100%;
    padding: 1%;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 0 1px 5px #dddddd;
    position: relative;
}


.page-bottom {
    width: 100%;
    height: 5%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.uni-pagination__total {
	margin-right: 0.4375rem !important;
}

.uni-pagination__num-tag {
	min-width: 1.875rem !important;
	background-color: #F0F0F0 !important;
}

.page--active {
	color: white !important;
	background-color: deeppink !important;
}

.table-container {
	width: 100%;
	height: 78%;
	margin-bottom: 1%;
	margin-top: 1%;
	display: block;
	table-layout: fixed; /* 使用固定布局 */
	border: 2px solid #ddd;
	padding: 1px;
}

table {
    background-color: #fff;
    table-layout: fixed; /* 使用固定布局 */
    border-collapse: collapse;
    position: relative;
    border-radius: 5px;
    box-sizing: border-box;
}

.rowClass {
    width: 150px;
    height: 2.5rem;
    border-right: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
    padding: 8px;
    text-align: center;
    box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
    overflow: visible; /* 允许内容溢出 */
    white-space: nowrap; /* 禁止换行 */
}


/*.rowClass::before {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 1px;
	background-color: #ddd;;
}

.rowClass::after {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 1px;
	height: 100%;
	background-color: #ddd;;
}*/




tr:nth-child(even) {
	background-color: #c2f2ff;
}
th {
	background-color: #f2f2f2; /* 表头背景色 */
	position: sticky; /* 固定表头 */
	top: 0; /* 固定在顶部 */
	z-index: 1; /* 确保表头在其他内容之上 */
	padding: 2px;
}

th::before {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 2px;
	background-color: #ddd;;
}

.ordNoClass{
	font-size: 14px;
	border: 1px solid #e5e5e5;
	box-sizing: border-box;
	border-radius: 4px;
	padding: 0 5px;
	padding-left: 10px;
	position: relative;
	display: flex;
	-webkit-user-select: none;
	user-select: none;
	flex-direction: row;
	align-items: center;
	width: 100%;
	flex: 1;
	height: 35px;

}

.flex_box{
	width: 320px;
	display: flex;
	align-items: center;
	margin-left:10px
}

</style>